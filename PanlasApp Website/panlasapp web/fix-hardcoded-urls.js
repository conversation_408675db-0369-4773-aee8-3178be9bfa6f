#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix all hardcoded localhost URLs in the React frontend
 * This script will replace all instances of 'http://localhost:5000/api' with environment variable usage
 */

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX files
function findJSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findJSFiles(filePath, fileList);
    } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to fix hardcoded URLs in a file
function fixHardcodedUrls(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern 1: Direct axios calls with hardcoded URLs
    const patterns = [
      // axios.get('http://localhost:5000/api/...
      {
        regex: /axios\.(get|post|put|delete|patch)\(\s*['"`]http:\/\/localhost:5000\/api([^'"`]*?)['"`]/g,
        replacement: (match, method, endpoint) => {
          const envVarLine = "const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';";
          // Check if API_BASE_URL is already defined in this function/scope
          const lines = content.split('\n');
          const matchLineIndex = content.substring(0, content.indexOf(match)).split('\n').length - 1;
          
          // Look backwards for function start or API_BASE_URL definition
          let hasApiBaseUrl = false;
          for (let i = matchLineIndex; i >= Math.max(0, matchLineIndex - 20); i--) {
            if (lines[i] && (lines[i].includes('const API_BASE_URL') || lines[i].includes('let API_BASE_URL'))) {
              hasApiBaseUrl = true;
              break;
            }
          }
          
          if (!hasApiBaseUrl) {
            // Find the start of the function and add API_BASE_URL there
            for (let i = matchLineIndex; i >= 0; i--) {
              if (lines[i] && (lines[i].includes('const ') || lines[i].includes('async ') || lines[i].includes('function ') || lines[i].includes('=>'))) {
                // Insert API_BASE_URL definition after this line
                const beforeFunction = lines.slice(0, i + 1).join('\n');
                const afterFunction = lines.slice(i + 1).join('\n');
                content = beforeFunction + '\n      ' + envVarLine + '\n' + afterFunction;
                break;
              }
            }
          }
          
          return `axios.${method}(\`\${API_BASE_URL}${endpoint}\``;
        }
      },
      
      // fetch('http://localhost:5000/api/...
      {
        regex: /fetch\(\s*['"`]http:\/\/localhost:5000\/api([^'"`]*?)['"`]/g,
        replacement: (match, endpoint) => {
          const envVarLine = "const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';";
          const lines = content.split('\n');
          const matchLineIndex = content.substring(0, content.indexOf(match)).split('\n').length - 1;
          
          let hasApiBaseUrl = false;
          for (let i = matchLineIndex; i >= Math.max(0, matchLineIndex - 20); i--) {
            if (lines[i] && (lines[i].includes('const API_BASE_URL') || lines[i].includes('let API_BASE_URL'))) {
              hasApiBaseUrl = true;
              break;
            }
          }
          
          if (!hasApiBaseUrl) {
            for (let i = matchLineIndex; i >= 0; i--) {
              if (lines[i] && (lines[i].includes('const ') || lines[i].includes('async ') || lines[i].includes('function ') || lines[i].includes('=>'))) {
                const beforeFunction = lines.slice(0, i + 1).join('\n');
                const afterFunction = lines.slice(i + 1).join('\n');
                content = beforeFunction + '\n      ' + envVarLine + '\n' + afterFunction;
                break;
              }
            }
          }
          
          return `fetch(\`\${API_BASE_URL}${endpoint}\``;
        }
      },
      
      // Simple string replacements for template literals and other cases
      {
        regex: /['"`]http:\/\/localhost:5000\/api([^'"`]*?)['"`]/g,
        replacement: (match, endpoint) => {
          return `\`\${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}${endpoint}\``;
        }
      },
      
      // Fix localhost:3000 references (frontend URLs)
      {
        regex: /http:\/\/localhost:3000/g,
        replacement: () => {
          return `${window.location.origin}`;
        }
      }
    ];
    
    // Apply each pattern
    patterns.forEach(pattern => {
      if (pattern.regex.test(content)) {
        content = content.replace(pattern.regex, pattern.replacement);
        modified = true;
      }
    });
    
    // Write back if modified
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found');
    process.exit(1);
  }
  
  console.log('🔍 Finding all JS/JSX files...');
  const jsFiles = findJSFiles(srcDir);
  console.log(`📁 Found ${jsFiles.length} JS/JSX files`);
  
  console.log('\n🔧 Fixing hardcoded URLs...');
  let fixedCount = 0;
  
  jsFiles.forEach(file => {
    if (fixHardcodedUrls(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n✨ Fixed ${fixedCount} files`);
  console.log('🎉 All hardcoded URLs have been updated to use environment variables!');
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { fixHardcodedUrls, findJSFiles };
