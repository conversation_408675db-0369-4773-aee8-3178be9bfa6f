#!/bin/bash

# Initial Server Setup Script for PanlasApp on Linode
# Run this script first on a fresh Ubuntu server

set -e

echo "🔧 Setting up server for PanlasApp deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

# Update system
print_status "Updating system packages..."
apt update && apt upgrade -y

# Install essential packages
print_status "Installing essential packages..."
apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Node.js 18
print_status "Installing Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Verify Node.js installation
node_version=$(node --version)
npm_version=$(npm --version)
print_status "Node.js installed: $node_version"
print_status "npm installed: $npm_version"

# Install MongoDB
print_status "Installing MongoDB..."
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-6.0.list
apt-get update
apt-get install -y mongodb-org

# Start and enable MongoDB
systemctl start mongod
systemctl enable mongod
print_status "MongoDB installed and started"

# Install Nginx
print_status "Installing Nginx..."
apt install nginx -y
systemctl start nginx
systemctl enable nginx
print_status "Nginx installed and started"

# Install PM2
print_status "Installing PM2..."
npm install -g pm2
print_status "PM2 installed"

# Install Certbot for SSL
print_status "Installing Certbot for SSL certificates..."
apt install certbot python3-certbot-nginx -y
print_status "Certbot installed"

# Configure firewall
print_status "Configuring firewall..."
ufw --force enable
ufw allow ssh
ufw allow 'Nginx Full'
ufw allow 80
ufw allow 443
print_status "Firewall configured"

# Create application user and directories
print_status "Setting up application directories..."
mkdir -p /var/www/panlasapp
mkdir -p /var/www/panlasapp/logs
mkdir -p /var/www/panlasapp/uploads
mkdir -p /var/backups/panlasapp
mkdir -p /var/backups/mongodb

# Set permissions
chown -R umer:umer /var/www/panlasapp
chown -R umer:umer /var/backups/panlasapp
chmod 755 /var/www/panlasapp

print_status "Application directories created"

# Configure log rotation
print_status "Setting up log rotation..."
cat > /etc/logrotate.d/panlasapp << EOF
/var/www/panlasapp/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 umer umer
    postrotate
        pm2 reloadLogs
    endscript
}
EOF

# Configure MongoDB log rotation
cat > /etc/logrotate.d/mongodb << EOF
/var/log/mongodb/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    sharedscripts
    postrotate
        /bin/kill -SIGUSR1 \$(cat /var/lib/mongodb/mongod.lock 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF

print_status "Log rotation configured"

# Install additional monitoring tools
print_status "Installing monitoring tools..."
apt install -y htop iotop nethogs

# Configure automatic security updates
print_status "Configuring automatic security updates..."
apt install -y unattended-upgrades
dpkg-reconfigure -plow unattended-upgrades

# Create backup script
print_status "Creating backup script..."
cat > /usr/local/bin/backup-panlasapp << 'EOF'
#!/bin/bash
# PanlasApp Backup Script

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/panlasapp"
MONGO_BACKUP_DIR="/var/backups/mongodb"

# Create backup directories
mkdir -p "$BACKUP_DIR"
mkdir -p "$MONGO_BACKUP_DIR"

# Backup MongoDB
echo "Backing up MongoDB..."
mongodump --db meal_planner_production --out "$MONGO_BACKUP_DIR/$DATE"

# Backup application files
echo "Backing up application files..."
tar -czf "$BACKUP_DIR/app_$DATE.tar.gz" -C /var/www panlasapp

# Keep only last 7 days of backups
find "$BACKUP_DIR" -name "app_*.tar.gz" -mtime +7 -delete
find "$MONGO_BACKUP_DIR" -type d -mtime +7 -exec rm -rf {} +

echo "Backup completed: $DATE"
EOF

chmod +x /usr/local/bin/backup-panlasapp

# Setup daily backup cron job
print_status "Setting up daily backup cron job..."
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-panlasapp >> /var/log/backup.log 2>&1") | crontab -

# Configure MongoDB for production
print_status "Configuring MongoDB for production..."
cat > /etc/mongod.conf << EOF
# mongod.conf

# for documentation of all options, see:
#   http://docs.mongodb.org/manual/reference/configuration-options/

# Where to store data
storage:
  dbPath: /var/lib/mongodb
  journal:
    enabled: true

# Where to write logging data
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

# Network interfaces
net:
  port: 27017
  bindIp: 127.0.0.1

# Process management
processManagement:
  timeZoneInfo: /usr/share/zoneinfo

# Security
security:
  authorization: enabled

# Operation profiling
operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp
EOF

# Restart MongoDB with new configuration
systemctl restart mongod

print_status "✅ Server setup completed successfully!"
print_status ""
print_status "Next steps:"
print_status "1. Clone your application repository to /var/www/panlasapp"
print_status "2. Run the deployment script: ./deployment/linode/deploy.sh"
print_status "3. Configure your environment variables"
print_status "4. Set up SSL certificate with: sudo certbot --nginx -d panlasapp.food"
print_status ""
print_status "Server is ready for PanlasApp deployment!"

# Display system information
print_status "System Information:"
echo "- OS: $(lsb_release -d | cut -f2)"
echo "- Node.js: $(node --version)"
echo "- npm: $(npm --version)"
echo "- MongoDB: $(mongod --version | head -n1)"
echo "- Nginx: $(nginx -v 2>&1)"
echo "- PM2: $(pm2 --version)"
echo "- Disk Space: $(df -h / | awk 'NR==2{print $4}') available"
echo "- Memory: $(free -h | awk 'NR==2{print $7}') available"
