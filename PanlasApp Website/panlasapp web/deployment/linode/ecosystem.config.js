// PM2 Ecosystem Configuration for PanlasApp on Linode
// This file configures PM2 process manager for production deployment

module.exports = {
  apps: [
    {
      // Application configuration
      name: 'panlasapp-backend',
      script: './PanlasApp Website/panlasapp web/meal-planner-backend/server.js',
      cwd: '/var/www/panlasapp',
      
      // Instance configuration
      instances: 2, // Run 2 instances for load balancing
      exec_mode: 'cluster', // Use cluster mode for better performance
      
      // Environment variables
      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      
      // Restart configuration
      autorestart: true,
      watch: false, // Disable watch in production
      max_memory_restart: '1G', // Restart if memory usage exceeds 1GB
      
      // Logging configuration
      log_file: '/var/www/panlasapp/logs/combined.log',
      out_file: '/var/www/panlasapp/logs/out.log',
      error_file: '/var/www/panlasapp/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Advanced configuration
      min_uptime: '10s', // Minimum uptime before considering the app stable
      max_restarts: 10, // Maximum number of restarts within restart_delay
      restart_delay: 4000, // Delay between restarts
      
      // Health monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // Process management
      kill_timeout: 5000, // Time to wait before force killing
      listen_timeout: 3000, // Time to wait for app to listen
      
      // Source map support
      source_map_support: true,
      
      // Ignore specific signals
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        '.git'
      ],
      
      // Custom configuration for production
      node_args: [
        '--max-old-space-size=1024' // Limit Node.js memory usage
      ],
      
      // Error handling
      exp_backoff_restart_delay: 100,
      
      // Monitoring
      pmx: true,
      
      // Custom environment variables for production
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000,
        PM2_SERVE_PATH: '/var/www/panlasapp/PanlasApp Website/panlasapp web/dist',
        PM2_SERVE_PORT: 3000,
        PM2_SERVE_SPA: true,
        PM2_SERVE_HOMEPAGE: '/index.html'
      }
    }
  ],

  // Deployment configuration (optional)
  deploy: {
    production: {
      user: 'umer',
      host: '***************',
      ref: 'origin/main',
      repo: '**************:yourusername/panlasapp.git', // Replace with your actual repo
      path: '/var/www/panlasapp',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  },

  // PM2+ monitoring configuration (optional)
  monitoring: {
    http: true,
    https: false,
    port: 9615
  }
};
