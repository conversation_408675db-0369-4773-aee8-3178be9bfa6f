#!/bin/bash

# PanlasApp Update Script for Linode
# This script updates the application with minimal downtime

set -e  # Exit on any error

echo "🔄 Starting PanlasApp update..."

# Configuration
APP_DIR="/var/www/panlasapp"
BACKEND_DIR="$APP_DIR/PanlasApp Website/panlasapp web/meal-planner-backend"
FRONTEND_DIR="$APP_DIR/PanlasApp Website/panlasapp web"
USER="umer"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Backup current version
backup_current() {
    print_status "Creating backup of current version..."
    
    BACKUP_DIR="/var/backups/panlasapp/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup built frontend
    if [ -d "$FRONTEND_DIR/dist" ]; then
        cp -r "$FRONTEND_DIR/dist" "$BACKUP_DIR/"
        print_status "Frontend backup created"
    fi
    
    # Backup environment files
    cp "$FRONTEND_DIR/.env" "$BACKUP_DIR/frontend.env" 2>/dev/null || true
    cp "$BACKEND_DIR/.env" "$BACKUP_DIR/backend.env" 2>/dev/null || true
    
    print_status "Backup created at: $BACKUP_DIR"
}

# Pull latest changes
pull_changes() {
    print_status "Pulling latest changes from repository..."
    
    cd "$APP_DIR"
    
    # Stash any local changes
    git stash push -m "Auto-stash before update $(date)"
    
    # Pull latest changes
    git pull origin main || git pull origin master
    
    print_status "Latest changes pulled successfully"
}

# Update dependencies
update_dependencies() {
    print_status "Updating dependencies..."
    
    # Update backend dependencies
    cd "$BACKEND_DIR"
    sudo -u $USER npm install --production
    
    # Update frontend dependencies
    cd "$FRONTEND_DIR"
    sudo -u $USER npm install
    
    print_status "Dependencies updated"
}

# Build frontend
build_frontend() {
    print_status "Building frontend..."
    
    cd "$FRONTEND_DIR"
    sudo -u $USER npm run build
    
    print_status "Frontend built successfully"
}

# Restart services
restart_services() {
    print_status "Restarting services..."
    
    # Restart PM2 processes
    sudo -u $USER pm2 restart all
    
    # Reload Nginx
    nginx -t && systemctl reload nginx
    
    print_status "Services restarted"
}

# Health check
health_check() {
    print_status "Performing health check..."
    
    # Wait a moment for services to start
    sleep 5
    
    # Check PM2 status
    if sudo -u $USER pm2 status | grep -q "online"; then
        print_status "✅ Backend is running"
    else
        print_error "❌ Backend health check failed"
        return 1
    fi
    
    # Check Nginx status
    if systemctl is-active --quiet nginx; then
        print_status "✅ Nginx is running"
    else
        print_error "❌ Nginx health check failed"
        return 1
    fi
    
    # Check if application responds
    if curl -f -s http://localhost:80 > /dev/null; then
        print_status "✅ Application is responding"
    else
        print_warning "⚠️  Application may not be responding on port 80"
    fi
    
    print_status "Health check completed"
}

# Rollback function
rollback() {
    print_error "Update failed. Rolling back..."
    
    # Find latest backup
    LATEST_BACKUP=$(ls -t /var/backups/panlasapp/ | head -n1)
    
    if [ -n "$LATEST_BACKUP" ]; then
        print_status "Rolling back to: $LATEST_BACKUP"
        
        # Restore frontend
        if [ -d "/var/backups/panlasapp/$LATEST_BACKUP/dist" ]; then
            rm -rf "$FRONTEND_DIR/dist"
            cp -r "/var/backups/panlasapp/$LATEST_BACKUP/dist" "$FRONTEND_DIR/"
        fi
        
        # Restore environment files
        cp "/var/backups/panlasapp/$LATEST_BACKUP/frontend.env" "$FRONTEND_DIR/.env" 2>/dev/null || true
        cp "/var/backups/panlasapp/$LATEST_BACKUP/backend.env" "$BACKEND_DIR/.env" 2>/dev/null || true
        
        # Restart services
        restart_services
        
        print_status "Rollback completed"
    else
        print_error "No backup found for rollback"
    fi
}

# Main update function
main() {
    print_status "Starting PanlasApp update process..."
    
    # Check if we're in the right directory
    if [ ! -d "$APP_DIR" ]; then
        print_error "Application directory not found: $APP_DIR"
        exit 1
    fi
    
    # Perform update steps
    backup_current
    
    if ! pull_changes; then
        print_error "Failed to pull changes"
        exit 1
    fi
    
    if ! update_dependencies; then
        print_error "Failed to update dependencies"
        rollback
        exit 1
    fi
    
    if ! build_frontend; then
        print_error "Failed to build frontend"
        rollback
        exit 1
    fi
    
    if ! restart_services; then
        print_error "Failed to restart services"
        rollback
        exit 1
    fi
    
    if ! health_check; then
        print_warning "Health check failed, but services appear to be running"
        print_warning "Please check the application manually"
    fi
    
    print_status "✅ Update completed successfully!"
    print_status ""
    print_status "Application status:"
    sudo -u $USER pm2 status
    print_status ""
    print_status "Your application is available at: https://panlasapp.food"
}

# Handle script arguments
case "${1:-}" in
    --rollback)
        rollback
        ;;
    --health-check)
        health_check
        ;;
    *)
        main "$@"
        ;;
esac
