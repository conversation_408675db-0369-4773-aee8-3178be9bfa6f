# Complete PanlasApp Linode Deployment Guide

## Server Information
- **IP Address**: ***************
- **Domain**: panlasapp.food
- **User**: umer
- **Password**: GDHee3voQErOJuf

## Pre-Deployment Checklist

### 1. Domain Configuration
Ensure your domain `panlasapp.food` is pointing to your Linode server:

```bash
# Check DNS resolution
nslookup panlasapp.food
dig panlasapp.food

# Should return: ***************
```

### 2. Server Access
Test SSH access to your server:

```bash
ssh umer@***************
# or
ssh <EMAIL>
```

### 3. Repository Setup
Ensure your code is in a Git repository and accessible from the server.

## Quick Deployment (Automated)

### Step 1: Connect to Server
```bash
ssh umer@***************
```

### Step 2: Clone Repository
```bash
# Replace with your actual repository URL
git clone https://github.com/yourusername/panlasapp.git /var/www/panlasapp
cd /var/www/panlasapp
```

### Step 3: Run Deployment Script
```bash
chmod +x deployment/linode/deploy.sh
sudo ./deployment/linode/deploy.sh
```

### Step 4: Configure Environment Variables
Edit the environment files with your actual values:

```bash
# Frontend environment
sudo nano "PanlasApp Website/panlasapp web/.env"

# Backend environment  
sudo nano "PanlasApp Website/panlasapp web/meal-planner-backend/.env"
```

### Step 5: Setup SSL Certificate
```bash
sudo certbot --nginx -d panlasapp.food -d www.panlasapp.food
```

### Step 6: Verify Deployment
```bash
# Check PM2 status
pm2 status

# Check Nginx status
sudo systemctl status nginx

# Check application
curl -I https://panlasapp.food
```

## Manual Deployment (Step by Step)

### 1. System Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### 2. Install MongoDB

```bash
# Import MongoDB public key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Update and install
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start and enable MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Verify installation
sudo systemctl status mongod
```

### 3. Install Nginx

```bash
# Install Nginx
sudo apt install nginx -y

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Verify installation
sudo systemctl status nginx
```

### 4. Install PM2

```bash
# Install PM2 globally
sudo npm install -g pm2

# Verify installation
pm2 --version
```

### 5. Application Setup

```bash
# Create application directory
sudo mkdir -p /var/www/panlasapp
sudo chown -R umer:umer /var/www/panlasapp

# Clone repository
git clone <your-repo-url> /var/www/panlasapp
cd /var/www/panlasapp

# Create necessary directories
mkdir -p logs uploads
```

### 6. Install Dependencies

```bash
# Backend dependencies
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
npm install --production

# Frontend dependencies and build
cd "../"
npm install
npm run build
```

### 7. Environment Configuration

```bash
# Copy environment templates
cp deployment/linode/.env.production "PanlasApp Website/panlasapp web/.env"
cp deployment/linode/.env.backend.production "PanlasApp Website/panlasapp web/meal-planner-backend/.env"

# Edit with your actual values
nano "PanlasApp Website/panlasapp web/.env"
nano "PanlasApp Website/panlasapp web/meal-planner-backend/.env"
```

**Important Environment Variables to Update:**

Frontend (.env):
- `VITE_API_URL=https://panlasapp.food/api`

Backend (.env):
- `MONGODB_URI=mongodb://localhost:27017/meal_planner_production`
- `JWT_SECRET=<generate-a-strong-secret>`
- `FRONTEND_URL=https://panlasapp.food`
- `CORS_ORIGIN=https://panlasapp.food`
- `EMAIL_USER=<your-brevo-email>`
- `EMAIL_PASS=<your-brevo-password>`

### 8. Nginx Configuration

```bash
# Copy Nginx configuration
sudo cp deployment/linode/nginx.conf /etc/nginx/sites-available/panlasapp.food

# Enable site
sudo ln -s /etc/nginx/sites-available/panlasapp.food /etc/nginx/sites-enabled/

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### 9. PM2 Configuration

```bash
# Start application with PM2
pm2 start deployment/linode/ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
# Follow the instructions provided by the command above
```

### 10. SSL Certificate Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d panlasapp.food -d www.panlasapp.food

# Verify auto-renewal
sudo certbot renew --dry-run
```

### 11. Firewall Configuration

```bash
# Enable UFW
sudo ufw enable

# Allow necessary ports
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 80
sudo ufw allow 443

# Check status
sudo ufw status
```

## Post-Deployment Tasks

### 1. Database Setup

```bash
# Connect to MongoDB
mongosh

# Create database and user (optional)
use meal_planner_production
db.createUser({
  user: "panlasapp",
  pwd: "secure_password",
  roles: ["readWrite"]
})
```

### 2. Seed Database (if needed)

```bash
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
npm run seed
```

### 3. Create Admin User (if needed)

```bash
# Use the make-admin script or create manually through the application
node make-admin.cjs
```

## Monitoring and Maintenance

### Application Monitoring

```bash
# Check PM2 status
pm2 status
pm2 logs
pm2 monit

# Check Nginx status
sudo systemctl status nginx
sudo tail -f /var/log/nginx/panlasapp_access.log
sudo tail -f /var/log/nginx/panlasapp_error.log

# Check MongoDB status
sudo systemctl status mongod
sudo tail -f /var/log/mongodb/mongod.log
```

### Application Updates

```bash
# Use the update script
cd /var/www/panlasapp
./deployment/linode/update.sh

# Or manually:
git pull origin main
npm install
npm run build
pm2 restart all
```

### Backup Strategy

```bash
# Database backup
mongodump --db meal_planner_production --out /var/backups/mongodb/$(date +%Y%m%d)

# Application backup
tar -czf /var/backups/panlasapp/app_$(date +%Y%m%d).tar.gz /var/www/panlasapp
```

## Troubleshooting

### Common Issues

1. **Application not starting**
   ```bash
   pm2 logs
   pm2 restart all
   ```

2. **Nginx 502 Bad Gateway**
   ```bash
   sudo systemctl status nginx
   pm2 status
   sudo tail -f /var/log/nginx/panlasapp_error.log
   ```

3. **Database connection issues**
   ```bash
   sudo systemctl status mongod
   mongosh --eval "db.adminCommand('ping')"
   ```

4. **SSL certificate issues**
   ```bash
   sudo certbot certificates
   sudo certbot renew
   ```

### Performance Optimization

1. **Enable Nginx caching**
2. **Configure MongoDB indexes**
3. **Monitor PM2 memory usage**
4. **Set up log rotation**

## Security Checklist

- [ ] Change default MongoDB authentication
- [ ] Configure firewall rules
- [ ] Set up fail2ban for SSH protection
- [ ] Regular security updates
- [ ] Monitor application logs
- [ ] Backup strategy in place
- [ ] SSL certificate auto-renewal configured

## Support and Maintenance

For ongoing support:
1. Monitor PM2 logs: `pm2 logs`
2. Check Nginx logs: `sudo tail -f /var/log/nginx/panlasapp_error.log`
3. Monitor system resources: `htop`, `df -h`, `free -m`
4. Regular updates: `./deployment/linode/update.sh`

Your PanlasApp should now be successfully deployed and accessible at https://panlasapp.food!
