#!/bin/bash

# PanlasApp Monitoring Script for Linode
# This script provides monitoring and health check functionality

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

# Function to check service status
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        print_status "$service is running"
        return 0
    else
        print_error "$service is not running"
        return 1
    fi
}

# Function to check PM2 processes
check_pm2() {
    print_header "PM2 Process Status"
    
    if command -v pm2 &> /dev/null; then
        pm2 status
        echo ""
        
        # Check if panlasapp-backend is running
        if pm2 list | grep -q "panlasapp-backend.*online"; then
            print_status "PanlasApp backend is running"
        else
            print_error "PanlasApp backend is not running"
        fi
    else
        print_error "PM2 is not installed"
    fi
}

# Function to check system resources
check_resources() {
    print_header "System Resources"
    
    # Memory usage
    echo "Memory Usage:"
    free -h
    echo ""
    
    # Disk usage
    echo "Disk Usage:"
    df -h /
    echo ""
    
    # CPU load
    echo "CPU Load:"
    uptime
    echo ""
    
    # Top processes by memory
    echo "Top 5 processes by memory:"
    ps aux --sort=-%mem | head -6
    echo ""
}

# Function to check network connectivity
check_network() {
    print_header "Network Connectivity"
    
    # Check if port 80 is listening
    if netstat -tuln | grep -q ":80 "; then
        print_status "Port 80 (HTTP) is listening"
    else
        print_warning "Port 80 (HTTP) is not listening"
    fi
    
    # Check if port 443 is listening
    if netstat -tuln | grep -q ":443 "; then
        print_status "Port 443 (HTTPS) is listening"
    else
        print_warning "Port 443 (HTTPS) is not listening"
    fi
    
    # Check if port 5000 is listening (backend)
    if netstat -tuln | grep -q ":5000 "; then
        print_status "Port 5000 (Backend) is listening"
    else
        print_error "Port 5000 (Backend) is not listening"
    fi
    
    # Test external connectivity
    if curl -s --max-time 5 http://google.com > /dev/null; then
        print_status "External connectivity is working"
    else
        print_error "External connectivity is not working"
    fi
}

# Function to check application health
check_app_health() {
    print_header "Application Health Check"
    
    # Check if application responds on localhost
    if curl -f -s http://localhost:80 > /dev/null; then
        print_status "Application responds on port 80"
    else
        print_error "Application does not respond on port 80"
    fi
    
    # Check API health endpoint
    if curl -f -s http://localhost:5000/api/health > /dev/null 2>&1; then
        print_status "Backend API is responding"
    else
        print_warning "Backend API health check failed"
    fi
    
    # Check if domain is accessible
    if curl -f -s -I https://panlasapp.food > /dev/null 2>&1; then
        print_status "Domain https://panlasapp.food is accessible"
    else
        print_warning "Domain https://panlasapp.food is not accessible"
    fi
}

# Function to check logs for errors
check_logs() {
    print_header "Recent Log Errors"
    
    echo "Recent Nginx errors (last 10):"
    tail -10 /var/log/nginx/panlasapp_error.log 2>/dev/null || echo "No Nginx error log found"
    echo ""
    
    echo "Recent PM2 errors (last 10):"
    pm2 logs --err --lines 10 2>/dev/null || echo "No PM2 logs found"
    echo ""
    
    echo "Recent system errors (last 5):"
    journalctl -p err --since "1 hour ago" --no-pager -n 5 2>/dev/null || echo "No recent system errors"
    echo ""
}

# Function to check SSL certificate
check_ssl() {
    print_header "SSL Certificate Status"
    
    if command -v certbot &> /dev/null; then
        echo "SSL Certificates:"
        certbot certificates 2>/dev/null || echo "No certificates found"
        echo ""
        
        # Check certificate expiry
        if openssl x509 -in /etc/letsencrypt/live/panlasapp.food/cert.pem -noout -dates 2>/dev/null; then
            print_status "SSL certificate information retrieved"
        else
            print_warning "Could not retrieve SSL certificate information"
        fi
    else
        print_warning "Certbot is not installed"
    fi
}

# Function to check database
check_database() {
    print_header "Database Status"
    
    if check_service mongod; then
        # Test MongoDB connection
        if mongosh --eval "db.adminCommand('ping')" --quiet > /dev/null 2>&1; then
            print_status "MongoDB connection successful"
            
            # Check database size
            echo "Database information:"
            mongosh meal_planner_production --eval "db.stats()" --quiet 2>/dev/null || echo "Could not retrieve database stats"
        else
            print_error "MongoDB connection failed"
        fi
    fi
}

# Function to show quick status
quick_status() {
    print_header "Quick Status Check"
    
    # Check essential services
    check_service nginx
    check_service mongod
    
    # Check PM2
    if pm2 list | grep -q "panlasapp-backend.*online"; then
        print_status "PanlasApp backend is running"
    else
        print_error "PanlasApp backend is not running"
    fi
    
    # Check disk space
    disk_usage=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
    if [ $disk_usage -lt 80 ]; then
        print_status "Disk usage: ${disk_usage}% (OK)"
    elif [ $disk_usage -lt 90 ]; then
        print_warning "Disk usage: ${disk_usage}% (Warning)"
    else
        print_error "Disk usage: ${disk_usage}% (Critical)"
    fi
    
    # Check memory usage
    memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ $memory_usage -lt 80 ]; then
        print_status "Memory usage: ${memory_usage}% (OK)"
    elif [ $memory_usage -lt 90 ]; then
        print_warning "Memory usage: ${memory_usage}% (Warning)"
    else
        print_error "Memory usage: ${memory_usage}% (Critical)"
    fi
}

# Function to restart services
restart_services() {
    print_header "Restarting Services"
    
    echo "Restarting PM2 processes..."
    pm2 restart all
    
    echo "Reloading Nginx..."
    sudo systemctl reload nginx
    
    print_status "Services restarted"
}

# Main function
main() {
    case "${1:-}" in
        --quick|-q)
            quick_status
            ;;
        --full|-f)
            quick_status
            echo ""
            check_pm2
            echo ""
            check_resources
            echo ""
            check_network
            echo ""
            check_app_health
            echo ""
            check_database
            echo ""
            check_ssl
            echo ""
            check_logs
            ;;
        --restart|-r)
            restart_services
            ;;
        --logs|-l)
            check_logs
            ;;
        --help|-h)
            echo "PanlasApp Monitoring Script"
            echo ""
            echo "Usage: $0 [option]"
            echo ""
            echo "Options:"
            echo "  --quick, -q     Quick status check (default)"
            echo "  --full, -f      Full system monitoring"
            echo "  --restart, -r   Restart services"
            echo "  --logs, -l      Check recent logs"
            echo "  --help, -h      Show this help message"
            ;;
        *)
            quick_status
            ;;
    esac
}

# Run main function with all arguments
main "$@"
