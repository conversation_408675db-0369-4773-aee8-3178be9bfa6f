#!/bin/bash

# PanlasApp Linode Deployment Script
# This script automates the deployment of PanlasApp on Linode server

set -e  # Exit on any error

echo "🚀 Starting PanlasApp deployment on Linode..."

# Configuration
APP_DIR="/var/www/panlasapp"
BACKEND_DIR="$APP_DIR/PanlasApp Website/panlasapp web/meal-planner-backend"
FRONTEND_DIR="$APP_DIR/PanlasApp Website/panlasapp web"
NGINX_SITE="panlasapp.food"
USER="umer"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root for system operations
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_status "Running as root - OK"
    else
        print_error "This script needs to be run as root for system configuration"
        print_status "Please run: sudo ./deploy.sh"
        exit 1
    fi
}

# Install system dependencies
install_dependencies() {
    print_status "Installing system dependencies..."
    
    # Update system
    apt update && apt upgrade -y
    
    # Install Node.js 18
    if ! command -v node &> /dev/null; then
        print_status "Installing Node.js 18..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
        apt-get install -y nodejs
    else
        print_status "Node.js already installed: $(node --version)"
    fi
    
    # Install MongoDB
    if ! command -v mongod &> /dev/null; then
        print_status "Installing MongoDB..."
        wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | apt-key add -
        echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-6.0.list
        apt-get update
        apt-get install -y mongodb-org
        systemctl start mongod
        systemctl enable mongod
    else
        print_status "MongoDB already installed"
    fi
    
    # Install Nginx
    if ! command -v nginx &> /dev/null; then
        print_status "Installing Nginx..."
        apt install nginx -y
    else
        print_status "Nginx already installed"
    fi
    
    # Install PM2
    if ! command -v pm2 &> /dev/null; then
        print_status "Installing PM2..."
        npm install -g pm2
    else
        print_status "PM2 already installed"
    fi
}

# Setup application directory
setup_app_directory() {
    print_status "Setting up application directory..."
    
    # Create directory if it doesn't exist
    mkdir -p $APP_DIR
    chown -R $USER:$USER $APP_DIR
    
    # Create logs and uploads directories
    mkdir -p $APP_DIR/logs
    mkdir -p $APP_DIR/uploads
    chown -R $USER:$USER $APP_DIR/logs
    chown -R $USER:$USER $APP_DIR/uploads
}

# Install application dependencies
install_app_dependencies() {
    print_status "Installing application dependencies..."
    
    # Backend dependencies
    if [ -d "$BACKEND_DIR" ]; then
        print_status "Installing backend dependencies..."
        cd "$BACKEND_DIR"
        sudo -u $USER npm install --production
    else
        print_error "Backend directory not found: $BACKEND_DIR"
        exit 1
    fi
    
    # Frontend dependencies and build
    if [ -d "$FRONTEND_DIR" ]; then
        print_status "Installing frontend dependencies and building..."
        cd "$FRONTEND_DIR"
        sudo -u $USER npm install
        sudo -u $USER npm run build
    else
        print_error "Frontend directory not found: $FRONTEND_DIR"
        exit 1
    fi
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Copy environment files
    if [ -f "$APP_DIR/deployment/linode/.env.production" ]; then
        cp "$APP_DIR/deployment/linode/.env.production" "$FRONTEND_DIR/.env"
        print_status "Frontend environment file copied"
    else
        print_warning "Frontend environment template not found"
    fi
    
    if [ -f "$APP_DIR/deployment/linode/.env.backend.production" ]; then
        cp "$APP_DIR/deployment/linode/.env.backend.production" "$BACKEND_DIR/.env"
        print_status "Backend environment file copied"
    else
        print_warning "Backend environment template not found"
    fi
    
    # Set proper permissions
    chown $USER:$USER "$FRONTEND_DIR/.env" 2>/dev/null || true
    chown $USER:$USER "$BACKEND_DIR/.env" 2>/dev/null || true
    chmod 600 "$FRONTEND_DIR/.env" 2>/dev/null || true
    chmod 600 "$BACKEND_DIR/.env" 2>/dev/null || true
}

# Configure Nginx
configure_nginx() {
    print_status "Configuring Nginx..."
    
    # Copy Nginx configuration
    if [ -f "$APP_DIR/deployment/linode/nginx.conf" ]; then
        cp "$APP_DIR/deployment/linode/nginx.conf" "/etc/nginx/sites-available/$NGINX_SITE"
        
        # Enable site
        ln -sf "/etc/nginx/sites-available/$NGINX_SITE" "/etc/nginx/sites-enabled/"
        
        # Remove default site
        rm -f /etc/nginx/sites-enabled/default
        
        # Test configuration
        if nginx -t; then
            print_status "Nginx configuration is valid"
            systemctl restart nginx
            systemctl enable nginx
        else
            print_error "Nginx configuration test failed"
            exit 1
        fi
    else
        print_error "Nginx configuration file not found"
        exit 1
    fi
}

# Setup PM2
setup_pm2() {
    print_status "Setting up PM2..."
    
    # Stop any existing PM2 processes
    sudo -u $USER pm2 delete all 2>/dev/null || true
    
    # Start application with PM2
    if [ -f "$APP_DIR/deployment/linode/ecosystem.config.js" ]; then
        cd $APP_DIR
        sudo -u $USER pm2 start "deployment/linode/ecosystem.config.js"
        sudo -u $USER pm2 save
        
        # Setup PM2 startup
        sudo -u $USER pm2 startup | grep -E '^sudo' | bash
    else
        print_error "PM2 ecosystem configuration not found"
        exit 1
    fi
}

# Setup SSL certificate
setup_ssl() {
    print_status "Setting up SSL certificate..."
    
    # Install certbot if not present
    if ! command -v certbot &> /dev/null; then
        apt install certbot python3-certbot-nginx -y
    fi
    
    # Get SSL certificate
    print_warning "SSL certificate setup requires manual intervention"
    print_status "Run the following command after deployment:"
    print_status "sudo certbot --nginx -d panlasapp.food -d www.panlasapp.food"
}

# Configure firewall
configure_firewall() {
    print_status "Configuring firewall..."
    
    # Enable UFW if not enabled
    if ! ufw status | grep -q "Status: active"; then
        ufw --force enable
    fi
    
    # Allow necessary ports
    ufw allow ssh
    ufw allow 'Nginx Full'
    ufw allow 80
    ufw allow 443
    
    print_status "Firewall configured"
}

# Main deployment function
main() {
    print_status "Starting PanlasApp deployment..."
    
    check_root
    install_dependencies
    setup_app_directory
    install_app_dependencies
    setup_environment
    configure_nginx
    setup_pm2
    configure_firewall
    setup_ssl
    
    print_status "✅ Deployment completed successfully!"
    print_status ""
    print_status "Next steps:"
    print_status "1. Configure your environment variables in:"
    print_status "   - $FRONTEND_DIR/.env"
    print_status "   - $BACKEND_DIR/.env"
    print_status "2. Set up SSL certificate:"
    print_status "   sudo certbot --nginx -d panlasapp.food -d www.panlasapp.food"
    print_status "3. Check application status:"
    print_status "   pm2 status"
    print_status "   sudo systemctl status nginx"
    print_status ""
    print_status "Your application should be available at: https://panlasapp.food"
}

# Run main function
main "$@"
