# PanlasApp Backend Production Environment Variables
# Linode Deployment Configuration

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/meal_planner_production

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random_for_production_2024
JWT_EXPIRATION=24h

# Server Configuration
PORT=5000
NODE_ENV=production

# Frontend URL (for email links and CORS)
FRONTEND_URL=https://panlasapp.food
CORS_ORIGIN=https://panlasapp.food

# Email Configuration (Brevo SMTP)
# Replace with your actual Brevo credentials
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-brevo-smtp-password
EMAIL_HOST=smtp-relay.brevo.com
EMAIL_PORT=587
EMAIL_SECURE=false

# Rate Limiting Configuration
MAX_LOGIN_ATTEMPTS=5
LOGIN_WINDOW_MS=900000
BLOCK_DURATION_MS=3600000

# Security Configuration
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=6

# API Configuration
API_BASE_URL=https://panlasapp.food/api

# Session Configuration
SESSION_SECRET=your_session_secret_here_make_it_random_and_long_for_production

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/var/www/panlasapp/uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=/var/www/panlasapp/logs/app.log

# Cache Configuration (optional - Redis)
# REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Third-party API Keys (if needed)
# NUTRITION_API_KEY=your_nutrition_api_key
# RECIPE_API_KEY=your_recipe_api_key

# Gemini AI Configuration (if using)
# GEMINI_API_KEY=your_gemini_api_key

# Production Security Headers
HELMET_ENABLED=true
TRUST_PROXY=true

# Domain Configuration
DOMAIN=panlasapp.food
SUBDOMAIN=www.panlasapp.food
